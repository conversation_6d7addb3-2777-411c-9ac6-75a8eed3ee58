import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def process_malang_data():
    print('Processing Malang weather and flood data...')
    
    # Load rainfall data
    rainfall_2022 = pd.read_csv('dataset/Ju<PERSON><PERSON> Malang, 2022.csv')
    rainfall_2023 = pd.read_csv('dataset/Jumlah <PERSON> Malang, 2023.csv')
    
    months = ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
              'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember']
    
    rainfall_data = []
    for year, df in [(2022, rainfall_2022), (2023, rainfall_2023)]:
        for i, month in enumerate(months):
            if month in df.columns:
                value = df[month].iloc[0]
                if pd.isna(value) or value == '-':
                    value = np.nan
                else:
                    value = float(str(value).replace(',', '.'))
                
                season = 'Musim_Hujan' if i+1 in [12,1,2] else 'Peralihan_1' if i+1 in [3,4,5] else 'Musim_Kemarau' if i+1 in [6,7,8] else 'Peralihan_2'
                
                rainfall_data.append({
                    'year': year,
                    'month': i + 1,
                    'month_name': month,
                    'rainfall_mm': value,
                    'season': season
                })
    
    df_rainfall = pd.DataFrame(rainfall_data)
    df_rainfall['date'] = pd.to_datetime(df_rainfall[['year', 'month']].assign(day=1))
    
    # Save processed data
    df_rainfall.to_csv('dataset/processed_rainfall_data.csv', index=False)
    
    print(f'Processed rainfall data: {df_rainfall.shape}')
    return df_rainfall

if __name__ == '__main__':
    df = process_malang_data()
    print('Data preprocessing completed!')
