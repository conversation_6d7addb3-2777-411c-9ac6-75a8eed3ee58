import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_flood_data():
    print('=== ANALISIS DATA BANJIR DAN GENANGAN DRAINASE ===')
    print('Kota Malang 2022-2023')
    print('=' * 60)
    
    # Load disaster data
    disaster_df = pd.read_csv('dataset/Jumlah <PERSON>cana Menurut Jenis Bencana dan Kecamatan di Kota Malang, 2022-2023.csv')
    
    # Process disaster data
    kecamatan_list = ['Kedungkandang', 'Sukun', 'Klojen', 'Blimbing', 'Lowokwaru', 'KOTA MALANG']
    disaster_data = []
    
    for i, kecamatan in enumerate(kecamatan_list):
        row_idx = i + 3
        
        for year in [2022, 2023]:
            banjir_col = 5 if year == 2022 else 6
            banjir_count = disaster_df.iloc[row_idx, banjir_col]
            if pd.isna(banjir_count):
                banjir_count = 0
            else:
                banjir_count = int(banjir_count)
            
            disaster_data.append({
                'year': year,
                'kecamatan': kecamatan,
                'banjir': banjir_count
            })
    
    df_disaster = pd.DataFrame(disaster_data)
    
    print('\nStatistik Banjir:')
    print('Total kejadian banjir per tahun:')
    yearly_floods = df_disaster.groupby('year')['banjir'].sum()
    print(yearly_floods)
    
    print('\nKejadian banjir per kecamatan:')
    district_floods = df_disaster.groupby('kecamatan')['banjir'].sum().sort_values(ascending=False)
    print(district_floods)
    
    # Calculate flood increase
    flood_2022 = yearly_floods[2022]
    flood_2023 = yearly_floods[2023]
    increase_pct = ((flood_2023 - flood_2022) / flood_2022) * 100
    
    print(f'\nPeningkatan banjir 2022 -> 2023: {increase_pct:.1f}%')
    
    # Create flood visualizations
    plt.figure(figsize=(15, 10))
    
    # 1. Flood trend by year
    plt.subplot(2, 3, 1)
    plt.bar(yearly_floods.index, yearly_floods.values, color=['blue', 'red'], alpha=0.7)
    plt.title('Total Kejadian Banjir per Tahun')
    plt.xlabel('Tahun')
    plt.ylabel('Jumlah Kejadian Banjir')
    for i, v in enumerate(yearly_floods.values):
        plt.text(yearly_floods.index[i], v + 5, str(v), ha='center', fontweight='bold')
    
    # 2. Flood by district
    plt.subplot(2, 3, 2)
    plt.bar(range(len(district_floods)), district_floods.values, color='orange', alpha=0.7)
    plt.title('Total Kejadian Banjir per Kecamatan (2022-2023)')
    plt.xlabel('Kecamatan')
    plt.ylabel('Jumlah Kejadian Banjir')
    plt.xticks(range(len(district_floods)), district_floods.index, rotation=45)
    
    # 3. Flood comparison by district and year
    plt.subplot(2, 3, 3)
    flood_pivot = df_disaster.pivot(index='kecamatan', columns='year', values='banjir')
    flood_pivot.plot(kind='bar', ax=plt.gca())
    plt.title('Perbandingan Banjir per Kecamatan')
    plt.xlabel('Kecamatan')
    plt.ylabel('Jumlah Kejadian Banjir')
    plt.xticks(rotation=45)
    plt.legend(['2022', '2023'])
    
    # 4. Heatmap of flood events
    plt.subplot(2, 3, 4)
    sns.heatmap(flood_pivot.T, annot=True, cmap='Reds', fmt='d')
    plt.title('Heatmap Kejadian Banjir')
    plt.xlabel('Kecamatan')
    plt.ylabel('Tahun')
    
    # 5. Flood risk assessment
    plt.subplot(2, 3, 5)
    # Calculate flood risk score (normalized)
    max_flood = district_floods.max()
    risk_scores = (district_floods / max_flood) * 100
    colors = ['green' if x < 30 else 'yellow' if x < 60 else 'red' for x in risk_scores]
    plt.bar(range(len(risk_scores)), risk_scores.values, color=colors, alpha=0.7)
    plt.title('Skor Risiko Banjir per Kecamatan')
    plt.xlabel('Kecamatan')
    plt.ylabel('Skor Risiko (%)')
    plt.xticks(range(len(risk_scores)), risk_scores.index, rotation=45)
    
    # 6. Flood increase by district
    plt.subplot(2, 3, 6)
    flood_change = df_disaster.pivot(index='kecamatan', columns='year', values='banjir')
    flood_change['increase'] = flood_change[2023] - flood_change[2022]
    flood_change['increase_pct'] = (flood_change['increase'] / flood_change[2022]) * 100
    flood_change['increase_pct'] = flood_change['increase_pct'].fillna(0)
    
    colors = ['green' if x <= 0 else 'red' for x in flood_change['increase']]
    plt.bar(range(len(flood_change)), flood_change['increase'], color=colors, alpha=0.7)
    plt.title('Perubahan Kejadian Banjir (2023 vs 2022)')
    plt.xlabel('Kecamatan')
    plt.ylabel('Perubahan Jumlah Banjir')
    plt.xticks(range(len(flood_change)), flood_change.index, rotation=45)
    plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    plt.savefig('dataset/hasil/03_flood_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Save processed flood data
    df_disaster.to_csv('dataset/processed_flood_data.csv', index=False)
    
    print('\nAnalisis banjir selesai!')
    print('Visualisasi disimpan ke: dataset/hasil/03_flood_analysis.png')
    print('Data banjir disimpan ke: dataset/processed_flood_data.csv')
    
    return df_disaster

if __name__ == '__main__':
    df_flood = analyze_flood_data()
