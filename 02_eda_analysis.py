import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def run_eda():
    print('=== EXPLORATORY DATA ANALYSIS ===')
    print('Dataset Curah Hujan Kota Malang 2022-2023')
    print('=' * 50)
    
    # Load data
    df_rainfall = pd.read_csv('dataset/processed_rainfall_data.csv')
    df_rainfall['date'] = pd.to_datetime(df_rainfall['date'])
    
    print('\nStatistik Deskriptif:')
    print(df_rainfall['rainfall_mm'].describe())
    
    print('\nCurah Hujan per Musim:')
    seasonal_stats = df_rainfall.groupby('season')['rainfall_mm'].agg(['mean', 'std', 'min', 'max'])
    print(seasonal_stats)
    
    print('\nCurah Hujan per Tahun:')
    yearly_stats = df_rainfall.groupby('year')['rainfall_mm'].agg(['mean', 'std', 'min', 'max'])
    print(yearly_stats)
    
    # Create visualizations
    plt.figure(figsize=(15, 10))
    
    # 1. Time series plot
    plt.subplot(2, 3, 1)
    plt.plot(df_rainfall['date'], df_rainfall['rainfall_mm'], marker='o', linewidth=2)
    plt.title('Tren Curah Hujan 2022-2023')
    plt.xlabel('Tanggal')
    plt.ylabel('Curah Hujan (mm)')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 2. Seasonal boxplot
    plt.subplot(2, 3, 2)
    sns.boxplot(data=df_rainfall, x='season', y='rainfall_mm')
    plt.title('Distribusi Curah Hujan per Musim')
    plt.xticks(rotation=45)
    
    # 3. Monthly pattern
    plt.subplot(2, 3, 3)
    monthly_avg = df_rainfall.groupby('month')['rainfall_mm'].mean()
    plt.bar(monthly_avg.index, monthly_avg.values, color='skyblue', alpha=0.8)
    plt.title('Rata-rata Curah Hujan per Bulan')
    plt.xlabel('Bulan')
    plt.ylabel('Curah Hujan (mm)')
    
    # 4. Yearly comparison
    plt.subplot(2, 3, 4)
    for year in [2022, 2023]:
        year_data = df_rainfall[df_rainfall['year'] == year]
        plt.plot(year_data['month'], year_data['rainfall_mm'], marker='o', label=str(year))
    plt.title('Perbandingan Curah Hujan 2022 vs 2023')
    plt.xlabel('Bulan')
    plt.ylabel('Curah Hujan (mm)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 5. Rainfall distribution
    plt.subplot(2, 3, 5)
    plt.hist(df_rainfall['rainfall_mm'].dropna(), bins=12, alpha=0.7, color='green', edgecolor='black')
    plt.title('Distribusi Curah Hujan')
    plt.xlabel('Curah Hujan (mm)')
    plt.ylabel('Frekuensi')
    
    # 6. Seasonal pattern by year
    plt.subplot(2, 3, 6)
    seasonal_yearly = df_rainfall.groupby(['year', 'season'])['rainfall_mm'].mean().unstack(level=0)
    seasonal_yearly.plot(kind='bar', ax=plt.gca())
    plt.title('Pola Musiman per Tahun')
    plt.xlabel('Musim')
    plt.ylabel('Rata-rata Curah Hujan (mm)')
    plt.legend(['2022', '2023'])
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig('dataset/hasil/02_eda_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print('\nVisualisasi disimpan ke: dataset/hasil/02_eda_analysis.png')
    print('EDA completed successfully!')

if __name__ == '__main__':
    run_eda()
